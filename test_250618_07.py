import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.pipeline import make_pipeline
from sklearn.feature_selection import SelectKBest, f_classif
import seaborn as sns

# 设置中文显示
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 加载石油溢出数据集
def load_data(file_path):
    """加载石油溢出数据集并返回特征和标签"""
    data = pd.read_csv(file_path)
    X = data.iloc[:, 1:-1].values  # 选择第1列到倒数第2列作为特征
    y = data.iloc[:, -1].values
    feature_names = data.columns[1:-1]  # 只包含实际选择的特征列名
    return X, y, feature_names

# 数据预处理和特征选择
def preprocess_data(X, y):
    """数据预处理，包括标准化和特征选择"""
    # 检测并移除常量特征
    std = np.std(X, axis=0)
    non_constant_mask = std > 1e-8
    X = X[:, non_constant_mask]
    
    # 数据标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 特征选择 (选择前10个最相关的特征)
    selector = SelectKBest(score_func=f_classif, k=min(10, X_scaled.shape[1]))
    X_selected = selector.fit_transform(X_scaled, y)
    
    # 更新特征掩码
    feature_mask = np.zeros(non_constant_mask.shape, dtype=bool)
    feature_mask[non_constant_mask] = selector.get_support()
    
    return X_selected, feature_mask

# 模型训练和评估
def train_and_evaluate(X, y):
    """训练SVM模型并评估性能"""
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # 创建SVM分类器
    svm = SVC(kernel='rbf', C=1.0, gamma='scale', class_weight='balanced', probability=True)
    
    # 5折交叉验证
    cv_scores = cross_val_score(svm, X_train, y_train, cv=5, scoring='f1')
    print(f"5折交叉验证 F1 分数: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
    
    # 训练模型
    svm.fit(X_train, y_train)
    
    # 在测试集上预测
    y_pred = svm.predict(X_test)
    y_prob = svm.predict_proba(X_test)[:, 1]  # 正类的概率
    
    # 计算评估指标
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f}")
    print(f"测试集精确率: {precision:.4f}")
    print(f"测试集召回率: {recall:.4f}")
    print(f"测试集 F1 分数: {f1:.4f}")
    
    return svm, X_train, X_test, y_train, y_test, y_pred, y_prob, {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'cv_f1_mean': np.mean(cv_scores),
        'cv_f1_std': np.std(cv_scores)
    }

# 可视化特征重要性
def plot_feature_importance(feature_names, feature_mask, top_n=10):
    """可视化特征重要性"""
    if len(feature_names) == 0:
        print("没有提供特征名称")
        return
    
    # 获取被选中的特征及其名称
    selected_features = feature_names[feature_mask]
    
    plt.figure(figsize=(10, 6))
    plt.barh(range(len(selected_features)), [1.0]*len(selected_features))
    plt.yticks(range(len(selected_features)), selected_features)
    plt.xlabel('重要性')
    plt.ylabel('特征名称')
    plt.title(f'选中的特征 (共{len(selected_features)}个)')
    plt.tight_layout()
    plt.show()

# 使用PCA降维可视化
def plot_pca(X, y):
    """使用PCA降维并可视化数据"""
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X)
    
    plt.figure(figsize=(10, 8))
    plt.scatter(X_pca[y==0, 0], X_pca[y==0, 1], c='blue', label='非溢出', alpha=0.7, s=50)
    plt.scatter(X_pca[y==1, 0], X_pca[y==1, 1], c='red', label='溢出', alpha=0.7, s=50)
    plt.xlabel('主成分1')
    plt.ylabel('主成分2')
    plt.title('PCA降维后的数据可视化')
    plt.legend()
    plt.tight_layout()
    plt.show()
    
    # 打印解释方差比
    print(f"PCA主成分解释方差比: {pca.explained_variance_ratio_}")
    print(f"累计解释方差比: {np.sum(pca.explained_variance_ratio_):.4f}")

# 使用t-SNE降维可视化
def plot_tsne(X, y):
    """使用t-SNE降维并可视化数据"""
    tsne = TSNE(n_components=2, random_state=42)
    X_tsne = tsne.fit_transform(X)
    
    plt.figure(figsize=(10, 8))
    plt.scatter(X_tsne[y==0, 0], X_tsne[y==0, 1], c='blue', label='非溢出', alpha=0.7, s=50)
    plt.scatter(X_tsne[y==1, 0], X_tsne[y==1, 1], c='red', label='溢出', alpha=0.7, s=50)
    plt.xlabel('t-SNE维度1')
    plt.ylabel('t-SNE维度2')
    plt.title('t-SNE降维后的数据可视化')
    plt.legend()
    plt.tight_layout()
    plt.show()

# 绘制混淆矩阵
def plot_confusion_matrix(y_test, y_pred):
    """绘制混淆矩阵"""
    cm = confusion_matrix(y_test, y_pred)
    
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['非溢出', '溢出'], 
                yticklabels=['非溢出', '溢出'])
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.title('混淆矩阵')
    plt.tight_layout()
    plt.show()

# 绘制ROC曲线
def plot_roc_curve(y_test, y_prob):
    """绘制ROC曲线"""
    from sklearn.metrics import roc_curve, auc
    
    fpr, tpr, _ = roc_curve(y_test, y_prob)
    roc_auc = auc(fpr, tpr)
    
    plt.figure(figsize=(8, 6))
    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC曲线 (面积 = {roc_auc:.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('假阳性率')
    plt.ylabel('真阳性率')
    plt.title('ROC曲线')
    plt.legend(loc="lower right")
    plt.tight_layout()
    plt.show()

# 绘制分类报告热图
def plot_classification_report(y_test, y_pred):
    """绘制分类报告热图"""
    report = classification_report(y_test, y_pred, target_names=['非溢出', '溢出'], output_dict=True)
    report_df = pd.DataFrame(report).transpose()
    
    # 移除支持列，因为它是整数，不适合热力图
    report_df = report_df[['precision', 'recall', 'f1-score']]
    
    plt.figure(figsize=(10, 6))
    sns.heatmap(report_df, annot=True, cmap='YlGnBu', fmt='.2f')
    plt.title('分类报告热力图')
    plt.tight_layout()
    plt.show()

# 主函数
def main():
    file_path = 'oil_spill.csv'  # 请替换为你的文件路径
    
    # 加载数据
    X, y, feature_names = load_data(file_path)
    
    # 打印数据集基本信息
    print(f"数据集形状: {X.shape}")
    print(f"正样本比例: {np.mean(y):.4f} ({np.sum(y)}/{len(y)})")
    
    # 数据预处理
    X_selected, feature_mask = preprocess_data(X, y)
    
    # 模型训练和评估
    svm, X_train, X_test, y_train, y_test, y_pred, y_prob, metrics = train_and_evaluate(X_selected, y)
    
    # 可视化
    plot_feature_importance(feature_names, feature_mask)
    plot_pca(X_selected, y)
    plot_tsne(X_selected, y)
    plot_confusion_matrix(y_test, y_pred)
    plot_roc_curve(y_test, y_prob)
    plot_classification_report(y_test, y_pred)
    
    # 保存结果
    results_df = pd.DataFrame(metrics, index=[0])
    results_df.to_csv('oil_spill_svm_results.csv', index=False)
    print("结果已保存至 oil_spill_svm_results.csv")

if __name__ == "__main__":
    main()