import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# 设置中文显示
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 加载数据
iris = load_iris()
X = iris.data[:, :2]  # 只使用前两个特征：萼片长度和宽度
y = iris.target
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=1)

# 创建并训练模型
clf = SVC(C=0.5, kernel='rbf')
clf.fit(X_train, y_train.ravel())

# 评估模型
print("训练集准确率: %.3f" % clf.score(X_train, y_train))
print("测试集准确率: %.3f" % clf.score(X_test, y_test))

def enhanced_draw(clf, X, y, feature_names=None):
    """增强的SVM分类结果可视化函数"""
    if feature_names is None:
        feature_names = ['sepal length', 'sepal width']
    
    # 确定网格范围
    x1_min, x1_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    x2_min, x2_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    
    # 创建网格
    x1, x2 = np.mgrid[x1_min:x1_max:200j, x2_min:x2_max:200j]
    grid_test = np.stack((x1.flat, x2.flat), axis=1)
    
    # 预测网格点的类别
    grid_hat = clf.predict(grid_test)
    grid_hat = grid_hat.reshape(x1.shape)
    
    # 定义颜色映射
    cm_light = mpl.colors.ListedColormap(['#A0FFA0', '#FFA0A0', '#A0A0FF'])  # 浅色背景
    cm_dark = mpl.colors.ListedColormap(['g', 'r', 'b'])  # 数据点颜色
    
    # 创建图形
    plt.figure(figsize=(12, 9))
    
    # 绘制决策边界和背景区域
    plt.pcolormesh(x1, x2, grid_hat, cmap=cm_light, alpha=0.8)
    
    # 绘制数据点，添加图例
    scatter = plt.scatter(X[:, 0], X[:, 1], c=y, edgecolors='k', s=60, cmap=cm_dark)
    plt.legend(handles=scatter.legend_elements()[0], 
               labels=['山鸢尾(Iris-setosa)', '变色鸢尾(Iris-versicolor)', '维吉尼亚鸢尾(Iris-virginica)'],
               title='鸢尾花品种',
               loc='upper right',
               fontsize=12)
    
    # 绘制支持向量
    plt.scatter(clf.support_vectors_[:, 0], clf.support_vectors_[:, 1], s=120, 
                facecolors='none', edgecolors='k', linewidths=1.5, label='支持向量')
    
    # 设置坐标轴标签和标题
    plt.xlabel(feature_names[0], fontsize=16)
    plt.ylabel(feature_names[1], fontsize=16)
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)
    plt.title("鸢尾花数据集的SVM分类结果", fontsize=20, pad=15)
    
    # 添加网格和图例
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    return plt

# 调用增强的可视化函数
plt = enhanced_draw(clf, X, y, ['萼片长度', '萼片宽度'])
plt.show()