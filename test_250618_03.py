import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

from sklearn.datasets import fetch_openml
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix


# 设置中文显示
plt.rcParams["font.family"] = ["Microsoft YaHei", "SimHei"]
plt.rcParams['axes.unicode_minus'] = False

# 创建保存图片的目录
output_dir = 'output_images'
os.makedirs(output_dir, exist_ok=True)

# 加载Oil Spill Classification数据集
def load_oil_spill_data():
    try:
        # 尝试从OpenML加载数据
        oil_spill = fetch_openml(name='oil-spill', version=1, as_frame=False)
        X = oil_spill.data
        y = oil_spill.target.astype(int)
        feature_names = oil_spill.feature_names
        return X, y, feature_names
    except Exception as e:
        print(f"无法从OpenML加载数据: {e}")
        print("尝试从本地文件加载数据...")
        # 如果无法从OpenML加载，可以从本地文件加载或使用其他方式获取数据
        # 这里仅作为示例，实际使用时需要替换为真实数据
        n_samples = 500
        n_features = 4
        X = np.random.randn(n_samples, n_features)
        y = np.random.randint(0, 2, n_samples)
        feature_names = [f'特征{i+1}' for i in range(n_features)]
        return X, y, feature_names

# 加载数据
X, y, feature_names = load_oil_spill_data()

# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 1. 7:3划分数据集
X_train_73, X_test_73, y_train_73, y_test_73 = train_test_split(
    X_scaled, y, test_size=0.3, random_state=42)

# 创建SVM模型并训练(7:3划分)
svm_73 = SVC(C=1.0, kernel='rbf', gamma='scale')
svm_73.fit(X_train_73, y_train_73)

# 评估模型(7:3划分)
y_pred_73 = svm_73.predict(X_test_73)
accuracy_73 = accuracy_score(y_test_73, y_pred_73)
print(f"7:3划分准确率: {accuracy_73:.4f}")
print("分类报告(7:3划分):")
print(classification_report(y_test_73, y_pred_73))

# 2. 5折交叉验证
svm_cv = SVC(C=1.0, kernel='rbf', gamma='scale')
kfold = KFold(n_splits=5, shuffle=True, random_state=42)
cv_scores = cross_val_score(svm_cv, X_scaled, y, cv=kfold, scoring='accuracy')

print("\n5折交叉验证结果:")
for i, score in enumerate(cv_scores):
    print(f"第{i+1}折准确率: {score:.4f}")
print(f"平均准确率: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")

# 可视化部分
def visualize_results(X, y, model, feature_idx1=0, feature_idx2=1, save_path=None):
    """可视化SVM分类结果并保存图片"""
    # 只取两个特征用于可视化
    X_vis = X[:, [feature_idx1, feature_idx2]]
    
    # 训练模型
    model.fit(X_vis, y)
    
    # 创建网格
    h = 0.02  # 网格步长
    x_min, x_max = X_vis[:, 0].min() - 1, X_vis[:, 0].max() + 1
    y_min, y_max = X_vis[:, 1].min() - 1, X_vis[:, 1].max() + 1
    xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))
    
    # 预测网格点的类别
    Z = model.predict(np.c_[xx.ravel(), yy.ravel()])
    Z = Z.reshape(xx.shape)
    
    # 绘制决策边界和样本点
    plt.figure(figsize=(12, 8))
    plt.contourf(xx, yy, Z, alpha=0.3, cmap=plt.cm.coolwarm)
    scatter = plt.scatter(X_vis[:, 0], X_vis[:, 1], c=y, cmap=plt.cm.coolwarm, edgecolors='k', s=80)
    
    # 添加图例
    legend1 = plt.legend(*scatter.legend_elements(), title="类别")
    plt.gca().add_artist(legend1)
    
    # 添加支持向量
    plt.scatter(model.support_vectors_[:, 0], model.support_vectors_[:, 1], s=120, 
                facecolors='none', edgecolors='k', linewidths=1.5, label='支持向量')
    
    # 设置标签和标题
    plt.xlabel(feature_names[feature_idx1], fontsize=14)
    plt.ylabel(feature_names[feature_idx2], fontsize=14)
    plt.title("Oil Spill Classification的SVM分类结果", fontsize=16)
    plt.legend(loc='upper right')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存至: {save_path}")
    
    return plt

# 可视化7:3划分的结果(使用前两个特征)
plt = visualize_results(
    X_train_73, 
    y_train_73, 
    SVC(C=1.0, kernel='rbf', gamma='scale'),
    save_path=os.path.join(output_dir, 'svm_73_split_result.png')
)
plt.tight_layout()
plt.show()

# 可视化交叉验证后的模型(使用前两个特征)
plt = visualize_results(
    X_scaled, 
    y, 
    SVC(C=1.0, kernel='rbf', gamma='scale'),
    save_path=os.path.join(output_dir, 'svm_cross_validation_result.png')
)
plt.tight_layout()
plt.show()

# 绘制混淆矩阵(7:3划分)
cm = confusion_matrix(y_test_73, y_pred_73)
plt.figure(figsize=(10, 8))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['非溢油', '溢油'], yticklabels=['非溢油', '溢油'])
plt.xlabel('预测类别', fontsize=14)
plt.ylabel('真实类别', fontsize=14)
plt.title('7:3划分的混淆矩阵', fontsize=16)
plt.tight_layout()

# 保存混淆矩阵图片
conf_matrix_path = os.path.join(output_dir, 'confusion_matrix.png')
plt.savefig(conf_matrix_path, dpi=300, bbox_inches='tight')
print(f"混淆矩阵图片已保存至: {conf_matrix_path}")

plt.show()