import numpy as np
import matplotlib as mpl
import matplotlib.pyplot as plt

from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# Step 1
iris = load_iris()

X = iris.data[:, :2]  # Only use sepal length and sepal width
y = iris.target

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.3, random_state=1)

# Step 2
def classifier():
    clf = SVC(C=0.2, kernel='linear', decision_function_shape='ovr')
    return clf

clf = SVC(C=0.5, kernel='linear')

# Step 3
def train_model(clf, X_train, y_train):
    clf.fit(X_train, y_train.ravel())
    return clf

train_model(clf, X_train, y_train)

# Step 4
def show_accuracy(a, b, tip):
    acc = a.ravel() == b.ravel()
    print("%s Accurarcy:%.3f" % (tip, np.mean(acc)))

def print_accuracy(clf, X_train, y_train, X_test, y_test):
    print("Train Accuracy: %.3f" % clf.score(X_train, y_train))
    print("Test Accuracy: %.3f" % clf.score(X_test, y_test))

    show_accuracy(clf.predict(X_train), y_train, "Train")

    show_accuracy(clf.predict(X_test), y_test, "Test")
    print('decision_function:\n', clf.decision_function(X_train))

# Step 5
def draw(clf, X):
    iris_feature  = 'sepal length', 'sepal width', 'petal length', 'petal width'

    x1_min, x1_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    x2_min, x2_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    x1, x2 = np.mgrid[x1_min:x1_max:200j, x2_min:x2_max:200j]
    grid_test = np.stack((x1.flat, x2.flat), axis=1)
    print("grid_test:", grid_test)
    
    z = clf.decision_function(grid_test)
    print("the distance to decision plane:\n", z)
    grid_hat = clf.predict(grid_test)

    print("grid_hat:", grid_hat)
    grid_hat = grid_hat.reshape(x1.shape)

    cm_light = mpl.colors.ListedColormap(['#A0FFA0', '#FFA0A0', '#A0A0FF'])
    cm_dark = mpl.colors.ListedColormap(['g', 'r', 'b'])
    plt.pcolormesh(x1, x2, grid_hat, cmap=cm_light)

    plt.scatter(X[:, 0], X[:, 1], c=np.squeeze(y), edgecolors='k', s=50, cmap=cm_dark)

    plt.xlabel(iris_feature[0], fontsize=20)
    plt.ylabel(iris_feature[1], fontsize=20)
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)
    plt.title("linear svm classification", fontsize=30)
#    plt.grid()
    plt.savefig('svm_linear_iris.png', dpi=300)

draw(clf, X)