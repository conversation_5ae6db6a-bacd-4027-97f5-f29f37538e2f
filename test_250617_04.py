import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# Step 1
def load_oil_spill_data(file_path='oil_spill.csv'):
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {df.shape[0]}条记录，{df.shape[1]}个特征")
        
        # 假设最后一列是目标变量(target)，其余列是特征
        X = df.iloc[:, 1:3].values
        y = df.iloc[:, -1].values
        
        # 获取特征名称
        feature_names = df.columns[1:-1].tolist()
        
        # 确保标签是数值类型
        if y.dtype == 'object':
            y = pd.factorize(y)[0]
            
        return X, y, feature_names
    
    except Exception as e:
        print(f"无法从CSV文件加载数据: {e}")
        print("请确保文件路径正确且格式无误")
        # 可选择返回随机数据或终止程序
        raise SystemExit("数据加载失败，请检查CSV文件")
    
X, y, feature_names = load_oil_spill_data()

# iris = load_iris()

# X = iris.data[:, :2]  # Only use sepal length and sepal width
# y = iris.target

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.3, random_state=1)

# Step 2
def classifier(kernel='linear', C=0.5):
    """创建SVM分类器"""
    clf = SVC(C=C, kernel=kernel, decision_function_shape='ovr')
    return clf

# Step 3
def train_model(clf, X_train, y_train):
    """训练模型"""
    clf.fit(X_train, y_train.ravel())
    return clf

# Step 4
def show_accuracy(a, b, tip):
    """显示准确率"""
    acc = a.ravel() == b.ravel()
    print("%s Accuracy: %.3f" % (tip, np.mean(acc)))

def print_accuracy(clf, X_train, y_train, X_test, y_test, kernel_name):
    """打印模型准确率信息"""
    print(f"\n=== {kernel_name.upper()} SVM 结果 ===")
    print("Train Accuracy: %.3f" % clf.score(X_train, y_train))
    print("Test Accuracy: %.3f" % clf.score(X_test, y_test))

    show_accuracy(clf.predict(X_train), y_train, "Train")
    show_accuracy(clf.predict(X_test), y_test, "Test")
    print('Decision Function Shape:', clf.decision_function(X_train).shape)

# Step 5 - 增强版绘图函数
def draw_svm_classification(clf, X, y, kernel_name, save_path=None):
    """
    增强的SVM分类结果可视化函数

    参数:
    - clf: 训练好的SVM分类器
    - X: 特征数据
    - y: 标签数据
    - kernel_name: 核函数名称
    - save_path: 保存路径，如果为None则不保存
    """
    # 智能计算数据范围 - 聚焦在数据密集区域
    # 使用更紧凑的范围来突出决策边界
    percentile_low, percentile_high = 2, 98
    x1_range = np.percentile(X[:, 0], [percentile_low, percentile_high])
    x2_range = np.percentile(X[:, 1], [percentile_low, percentile_high])

    # 动态调整边界，确保包含足够的决策边界信息
    margin_factor = 0.1  # 减小边界以聚焦数据区域
    x1_margin = (x1_range[1] - x1_range[0]) * margin_factor
    x2_margin = (x2_range[1] - x2_range[0]) * margin_factor

    x1_min, x1_max = x1_range[0] - x1_margin, x1_range[1] + x1_margin
    x2_min, x2_max = x2_range[0] - x2_margin, x2_range[1] + x2_margin

    # 高分辨率网格生成
    grid_resolution = 500  # 进一步提高分辨率
    x1, x2 = np.mgrid[x1_min:x1_max:complex(0, grid_resolution),
                      x2_min:x2_max:complex(0, grid_resolution)]
    grid_points = np.c_[x1.ravel(), x2.ravel()]

    # 预测网格点和决策函数值
    try:
        grid_predictions = clf.predict(grid_points)
        grid_predictions = grid_predictions.reshape(x1.shape)

        # 获取决策函数值用于绘制置信度等高线
        if hasattr(clf, 'decision_function'):
            decision_values = clf.decision_function(grid_points)
            if decision_values.ndim > 1:
                decision_values = decision_values[:, 0]  # 对于多类，取第一个
            decision_values = decision_values.reshape(x1.shape)
        else:
            decision_values = None

    except Exception as e:
        print(f"预测网格点时出错: {e}")
        return

    # 创建更大的图形
    plt.figure(figsize=(14, 10))

    # 设置高对比度颜色映射
    unique_labels = np.unique(y)
    n_classes = len(unique_labels)

    if n_classes == 2:
        # 使用更鲜明的颜色对比
        cm_light = mpl.colors.ListedColormap(['#B3E5FC', '#FFCDD2'])  # 浅蓝和浅红
        cm_dark = mpl.colors.ListedColormap(['#0D47A1', '#D32F2F'])   # 深蓝和深红
        boundary_colors = ['#1565C0', '#C62828']  # 边界颜色
    else:
        # 多类分类颜色
        colors_light = plt.cm.Pastel1(np.linspace(0, 1, n_classes))
        colors_dark = plt.cm.Dark2(np.linspace(0, 1, n_classes))
        cm_light = mpl.colors.ListedColormap(colors_light)
        cm_dark = mpl.colors.ListedColormap(colors_dark)
        boundary_colors = plt.cm.Dark2(np.linspace(0, 1, n_classes))

    # 绘制决策区域 - 使用更高的透明度
    contour_fill = plt.contourf(x1, x2, grid_predictions, alpha=0.4, cmap=cm_light,
                               levels=np.arange(n_classes+1)-0.5)

    # 绘制主决策边界 - 更粗更明显
    main_contour = plt.contour(x1, x2, grid_predictions, colors='black',
                              linewidths=3, alpha=0.9, levels=np.arange(n_classes+1)-0.5)

    # 绘制置信度等高线（如果可用）
    if decision_values is not None and n_classes == 2:
        confidence_levels = [-2, -1, -0.5, 0.5, 1, 2]  # 置信度等级
        confidence_contour = plt.contour(x1, x2, decision_values,
                                       levels=confidence_levels, colors='gray',
                                       linewidths=1, alpha=0.6, linestyles='dashed')
        plt.clabel(confidence_contour, inline=True, fontsize=8, fmt='%.1f')

    # 筛选显示范围内的数据点
    in_range_mask = ((X[:, 0] >= x1_min) & (X[:, 0] <= x1_max) &
                     (X[:, 1] >= x2_min) & (X[:, 1] <= x2_max))

    X_display = X[in_range_mask]
    y_display = y[in_range_mask]

    # 绘制普通数据点
    if len(X_display) > 0:
        scatter = plt.scatter(X_display[:, 0], X_display[:, 1],
                             c=y_display, cmap=cm_dark,
                             edgecolors='white', linewidth=1, s=80, alpha=0.8, zorder=5)

    # 标识支持向量
    if hasattr(clf, 'support_'):
        support_vectors = clf.support_
        # 筛选在显示范围内的支持向量
        sv_in_range = []
        sv_labels = []

        for sv_idx in support_vectors:
            if sv_idx < len(X) and in_range_mask[sv_idx]:
                sv_in_range.append(X[sv_idx])
                sv_labels.append(y[sv_idx])

        if sv_in_range:
            sv_array = np.array(sv_in_range)
            sv_labels = np.array(sv_labels)

            # 用大圆圈标识支持向量
            plt.scatter(sv_array[:, 0], sv_array[:, 1],
                       c=sv_labels, cmap=cm_dark,
                       s=200, alpha=0.8, marker='o',
                       edgecolors='yellow', linewidth=3,
                       label=f'Support Vectors ({len(sv_array)})', zorder=6)

    # 设置图形属性
    plt.xlabel('Feature 1', fontsize=14, fontweight='bold')
    plt.ylabel('Feature 2', fontsize=14, fontweight='bold')
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)

    # 根据核函数类型设置不同的标题
    kernel_desc = {
        'linear': 'Linear Kernel (直线决策边界)',
        'rbf': 'RBF Kernel (非线性决策边界)',
        'poly': 'Polynomial Kernel',
        'sigmoid': 'Sigmoid Kernel'
    }
    title = f'SVM Classification - {kernel_desc.get(kernel_name.lower(), kernel_name.upper())}'
    plt.title(title, fontsize=16, fontweight='bold', pad=20)

    # 添加网格
    plt.grid(True, alpha=0.3, linestyle=':', linewidth=0.5)

    # 添加图例
    if hasattr(clf, 'support_'):
        plt.legend(loc='upper right', fontsize=10, framealpha=0.9)

    # 添加详细统计信息
    n_support = len(clf.support_) if hasattr(clf, 'support_') else 0
    info_text = (f'核函数: {kernel_name.upper()}\n'
                f'支持向量数: {n_support}\n'
                f'数据范围 ({percentile_high-percentile_low}%):\n'
                f'Feature 1: [{x1_range[0]:.1f}, {x1_range[1]:.1f}]\n'
                f'Feature 2: [{x2_range[0]:.1f}, {x2_range[1]:.1f}]\n'
                f'显示点数: {np.sum(in_range_mask)}/{len(X)}')

    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5",
             facecolor="lightyellow", edgecolor="orange", alpha=0.95),
             fontsize=10, fontweight='bold')

    # 添加颜色条（对于多类分类）
    if n_classes > 2:
        cbar = plt.colorbar(scatter, label='Class', shrink=0.8)
        cbar.ax.tick_params(labelsize=10)

    plt.tight_layout()

    # 保存高质量图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"增强版图片已保存到: {save_path}")

    plt.close()  # 关闭图形以释放内存

# 运行两种SVM方法
kernels = ['linear', 'rbf']
results = {}

for kernel in kernels:
    print(f"\n{'='*50}")
    print(f"运行 {kernel.upper()} SVM")
    print(f"{'='*50}")

    # 创建和训练模型
    clf = classifier(kernel=kernel, C=0.5)
    clf = train_model(clf, X_train, y_train)

    # 打印准确率
    print_accuracy(clf, X_train, y_train, X_test, y_test, kernel)

    # 绘制并保存图片
    save_path = f'svm_{kernel}_classification.png'
    draw_svm_classification(clf, X, y, kernel, save_path)

    # 保存结果
    results[kernel] = {
        'model': clf,
        'train_accuracy': clf.score(X_train, y_train),
        'test_accuracy': clf.score(X_test, y_test)
    }

# 比较结果
print(f"\n{'='*50}")
print("模型比较结果")
print(f"{'='*50}")
for kernel, result in results.items():
    print(f"{kernel.upper()} SVM - 训练准确率: {result['train_accuracy']:.3f}, 测试准确率: {result['test_accuracy']:.3f}")

print(f"\n图片已保存:")
for kernel in kernels:
    print(f"- svm_{kernel}_classification.png")