import pandas as pd

# 读取 csv 文件
df = pd.read_csv('oil_spill.csv')

# 假设要查看的列名为 'column_name'，将其替换为实际的列名
column_name = 'f_2'

# 检查列是否存在
if column_name not in df.columns:
    print(f"列 '{column_name}' 不存在于文件中")
else:
    # 查看数值分布（这里只是简单地显示一些统计信息）
    print(f"列 '{column_name}' 的数值分布：")
    print(df[column_name].describe())

    # 获取最大的 20 个数
    top_20 = df[column_name].nlargest(20)

    print(f"\n列 '{column_name}' 最大的 20 个数：")
    print(top_20)