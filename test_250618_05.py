# import pandas as pd
# from sklearn.model_selection import train_test_split, cross_val_score
# from sklearn.svm import SVC
# from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
# import numpy as np

# # 加载数据集
# data = pd.read_csv('oil_spill.csv')

# # 假设最后一列是目标变量，其余列是特征
# X = data.iloc[:, :-1]
# y = data.iloc[:, -1]

# # 划分数据集为训练集和测试集
# X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# # 创建 SVM 分类器
# svm_classifier = SVC(kernel='rbf', C=1.0, gamma='scale')

# # 进行 5 折交叉验证
# cv_scores = cross_val_score(svm_classifier, X_train, y_train, cv=5)

# # 输出交叉验证的结果
# print(f'5 折交叉验证准确率: {cv_scores}')
# print(f'5 折交叉验证平均准确率: {np.mean(cv_scores)}')

# # 在训练集上训练模型
# svm_classifier.fit(X_train, y_train)

# # 在测试集上进行预测
# y_pred = svm_classifier.predict(X_test)

# # 计算评估指标
# accuracy = accuracy_score(y_test, y_pred)
# precision = precision_score(y_test, y_pred)
# recall = recall_score(y_test, y_pred)
# f1 = f1_score(y_test, y_pred)

# # 打印评估指标
# print(f'测试集准确率: {accuracy}')
# print(f'测试集精确率: {precision}')
# print(f'测试集召回率: {recall}')
# print(f'测试集 F1 分数: {f1}')

# # 将运行结果保存到文件
# result_dict = {
#     '5 折交叉验证准确率': cv_scores,
#     '5 折交叉验证平均准确率': np.mean(cv_scores),
#     '测试集准确率': accuracy,
#     '测试集精确率': precision,
#     '测试集召回率': recall,
#     '测试集 F1 分数': f1
# }

# result_df = pd.DataFrame.from_dict(result_dict, orient='index')
# result_df.to_csv('oil_spill_classification_results.csv', header=False)



import pandas as pd
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import numpy as np

# 加载数据集
data = pd.read_csv('oil_spill.csv')

# 假设最后一列是目标变量，其余列是特征
X = data.iloc[:, 1:-1]
y = data.iloc[:, -1]

# 查看正负样本数量
positive_samples = np.sum(y == 1)
negative_samples = np.sum(y == 0)
print(f'正样本数量（溢油）: {positive_samples}')
print(f'负样本数量（非溢油）: {negative_samples}')

# 划分数据集为训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 创建 SVM 分类器，设置 class_weight='balanced' 处理样本不平衡
svm_classifier = SVC(kernel='rbf', C=1.0, gamma='scale', class_weight='balanced')

# 进行 5 折交叉验证
cv_scores = cross_val_score(svm_classifier, X_train, y_train, cv=5)

# 输出交叉验证的结果
print(f'5 折交叉验证准确率: {cv_scores}')
print(f'5 折交叉验证平均准确率: {np.mean(cv_scores)}')

# 在训练集上训练模型
svm_classifier.fit(X_train, y_train)

# 在测试集上进行预测
y_pred = svm_classifier.predict(X_test)

# 计算评估指标
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, zero_division=0)
recall = recall_score(y_test, y_pred, zero_division=0)
f1 = f1_score(y_test, y_pred, zero_division=0)

# 打印评估指标
print(f'测试集准确率: {accuracy}')
print(f'测试集精确率: {precision}')
print(f'测试集召回率: {recall}')
print(f'测试集 F1 分数: {f1}')

# 将运行结果保存到文件
result_dict = {
    '5 折交叉验证准确率': [cv_scores],
    '5 折交叉验证平均准确率': [np.mean(cv_scores)],
    '测试集准确率': [accuracy],
    '测试集精确率': [precision],
    '测试集召回率': [recall],
    '测试集 F1 分数': [f1]
}

result_df = pd.DataFrame.from_dict(result_dict, orient='index').explode(0)
result_df.to_csv('oil_spill_classification_results.csv', header=False)