import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix, roc_curve, auc, RocCurveDisplay
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import seaborn as sns

# 加载数据集
df = pd.read_csv('oil_spill.csv')

# 数据预处理
X = df.drop(columns=['target', 'f_1'])  # 移除目标列和第一列（可能是索引）
y = df['target']

# 标准化特征
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.3, random_state=42, stratify=y
)

# 创建SVM模型
svm_model = SVC(
    kernel='rbf', 
    C=1.0, 
    gamma='scale',
    class_weight='balanced',
    probability=True,
    random_state=42
)

# 训练模型
svm_model.fit(X_train, y_train)

# 在测试集上评估
y_pred = svm_model.predict(X_test)
y_prob = svm_model.predict_proba(X_test)[:, 1]  # 正类的概率

print("测试集性能评估")
print("="*50)
print(f"测试集准确率: {accuracy_score(y_test, y_pred):.4f}")
print("\n分类报告:\n", classification_report(y_test, y_pred))
print("\n混淆矩阵:")
print(confusion_matrix(y_test, y_pred))

# 5折交叉验证
skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
cv_scores = cross_val_score(svm_model, X_scaled, y, cv=skf, scoring='accuracy')
print("\n5折交叉验证结果")
print("="*50)
print(f"各折准确率: {cv_scores}")
print(f"平均交叉验证准确率: {np.mean(cv_scores):.4f} (±{np.std(cv_scores):.4f})")

# ===================== 可视化部分 =====================
plt.figure(figsize=(15, 12))

# 1. 混淆矩阵可视化
plt.subplot(2, 2, 1)
cm = confusion_matrix(y_test, y_pred)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['Non-Spill', 'Spill'],
            yticklabels=['Non-Spill', 'Spill'])
plt.xlabel('Predicted')
plt.ylabel('True')
plt.title('Confusion Matrix')

# 2. ROC曲线
plt.subplot(2, 2, 2)
fpr, tpr, thresholds = roc_curve(y_test, y_prob)
roc_auc = auc(fpr, tpr)
plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.2f})')
plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.title('Receiver Operating Characteristic (ROC)')
plt.legend(loc="lower right")

# 3. PCA降维可视化
plt.subplot(2, 2, 3)
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X_scaled)

# 创建简化版SVM用于可视化
svm_visual = SVC(kernel='rbf', C=1.0, gamma='scale', probability=True, random_state=42)
svm_visual.fit(X_pca, y)

# 创建网格点
x_min, x_max = X_pca[:, 0].min() - 1, X_pca[:, 0].max() + 1
y_min, y_max = X_pca[:, 1].min() - 1, X_pca[:, 1].max() + 1
xx, yy = np.meshgrid(np.arange(x_min, x_max, 0.02),
                     np.arange(y_min, y_max, 0.02))

# 预测网格点类别
Z = svm_visual.predict(np.c_[xx.ravel(), yy.ravel()])
Z = Z.reshape(xx.shape)

# 绘制决策边界和散点图
plt.contourf(xx, yy, Z, alpha=0.8, cmap=plt.cm.coolwarm)
plt.scatter(X_pca[:, 0], X_pca[:, 1], c=y, edgecolors='k', 
            cmap=plt.cm.coolwarm, s=40)
plt.xlabel('Principal Component 1')
plt.ylabel('Principal Component 2')
plt.title('PCA Projection with SVM Decision Boundaries')
plt.colorbar()

# 4. 支持向量可视化
plt.subplot(2, 2, 4)
# 获取支持向量在PCA空间中的位置
support_vectors = svm_visual.support_vectors_
plt.scatter(X_pca[:, 0], X_pca[:, 1], c=y, cmap=plt.cm.coolwarm, s=30, alpha=0.6)
plt.scatter(support_vectors[:, 0], support_vectors[:, 1], 
            facecolors='none', edgecolors='gold', s=100, 
            linewidths=1.5, label='Support Vectors')
plt.xlabel('Principal Component 1')
plt.ylabel('Principal Component 2')
plt.title('Support Vectors in PCA Space')
plt.legend()

plt.tight_layout()
plt.show()

# ===================== 特征重要性分析 =====================
# 使用模型系数分析特征重要性
if hasattr(svm_model, 'coef_'):
    importance = np.abs(svm_model.coef_[0])
else:
    # 对于RBF核SVM，使用基于排列的特征重要性
    from sklearn.inspection import permutation_importance
    result = permutation_importance(svm_model, X_test, y_test, n_repeats=10, random_state=42)
    importance = result.importances_mean

feature_names = X.columns
indices = np.argsort(importance)[::-1]

plt.figure(figsize=(12, 8))
plt.title("Top 20 Important Features")
plt.bar(range(20), importance[indices[:20]], color='skyblue', align='center')
plt.xticks(range(20), [feature_names[i] for i in indices[:20]], rotation=45, ha='right')
plt.xlim([-1, 20])
plt.xlabel('Feature Index')
plt.ylabel('Importance Score')
plt.tight_layout()
plt.show()

# 输出最重要的特征
print("\nTop 10 Important Features:")
for i in range(10):
    print(f"{i+1}. {feature_names[indices[i]]}: {importance[indices[i]]:.4f}")