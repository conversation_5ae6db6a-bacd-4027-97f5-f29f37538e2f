import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os

from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score


# 设置中文显示
plt.rcParams["font.family"] = ["Microsoft YaHei", "SimHei"]
plt.rcParams['axes.unicode_minus'] = False

# 创建保存图片的目录
output_dir = 'output_images'
os.makedirs(output_dir, exist_ok=True)

# 加载数据
def load_data(file_path='oil_spill.csv'):
    try:
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {df.shape[0]}条记录，{df.shape[1]}个特征")
        return df
    except Exception as e:
        print(f"无法从CSV文件加载数据: {e}")
        raise SystemExit("数据加载失败，请检查CSV文件")

# 加载数据
data = load_data()

# 假设第一列是序号，最后一列是目标变量
X = data.iloc[:, 1:-1]  # 跳过第一列（序号列），选择特征列
y = data.iloc[:, -1]    # 选择最后一列作为目标变量

# 数据标准化
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# 聚类分析
def perform_clustering(X, n_clusters=2):
    """执行k-means聚类并返回聚类结果"""
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    cluster_labels = kmeans.fit_predict(X)
    return kmeans, cluster_labels

# 进行聚类（假设分为2个簇）
n_clusters = 2
kmeans, cluster_labels = perform_clustering(X_scaled, n_clusters)

# 可视化聚类结果
def visualize_clustering(X, cluster_labels, cluster_centers=None, feature_idx1=0, feature_idx2=1, save_path=None):
    """可视化聚类结果并保存图片"""
    # 只取两个特征用于可视化
    X_vis = X[:, [feature_idx1, feature_idx2]]
    
    plt.figure(figsize=(12, 8))
    
    # 绘制聚类结果
    scatter = plt.scatter(X_vis[:, 0], X_vis[:, 1], c=cluster_labels, cmap='viridis', s=80, edgecolors='k')
    
    # 添加聚类中心
    if cluster_centers is not None:
        centers = cluster_centers[:, [feature_idx1, feature_idx2]]
        plt.scatter(centers[:, 0], centers[:, 1], s=150, c='red', marker='X', label='聚类中心')
    
    # 添加图例
    plt.legend(*scatter.legend_elements(), title="簇")
    plt.xlabel(X.columns[feature_idx1], fontsize=14)
    plt.ylabel(X.columns[feature_idx2], fontsize=14)
    plt.title(f"k-means聚类结果 (n_clusters={n_clusters})", fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存至: {save_path}")
    
    return plt

# 可视化聚类结果（使用前两个特征）
plt = visualize_clustering(
    X_scaled,
    cluster_labels,
    kmeans.cluster_centers_,
    feature_idx1=0,
    feature_idx2=1,
    save_path=os.path.join(output_dir, 'clustering_result.png')
)
plt.tight_layout()
plt.show()

# 评估聚类质量（轮廓系数）
silhouette_avg = silhouette_score(X_scaled, cluster_labels)
print(f"聚类的轮廓系数: {silhouette_avg:.4f}")

# 绘制轮廓系数图
def plot_silhouette_analysis(X, cluster_labels, save_path=None):
    """绘制轮廓系数分析图"""
    silhouette_values = silhouette_score(X, cluster_labels, sample_size=None)
    plt.figure(figsize=(10, 7))
    sns.histplot(silhouette_values, kde=True, color="blue")
    plt.title("轮廓系数分布", fontsize=16)
    plt.xlabel("轮廓系数", fontsize=14)
    plt.ylabel("样本数量", fontsize=14)
    plt.axvline(x=silhouette_avg, color='red', linestyle='--', label=f'平均轮廓系数: {silhouette_avg:.4f}')
    plt.legend()
    plt.tight_layout()

    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"轮廓系数图已保存至: {save_path}")
    
    return plt

# 绘制轮廓系数图
plt = plot_silhouette_analysis(
    X_scaled,
    cluster_labels,
    save_path=os.path.join(output_dir, 'silhouette_analysis.png')
)
plt.show()