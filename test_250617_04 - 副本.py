import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# Step 1
def load_oil_spill_data(file_path='oil_spill.csv'):
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {df.shape[0]}条记录，{df.shape[1]}个特征")
        
        # 假设最后一列是目标变量(target)，其余列是特征
        X = df.iloc[:, 1:3].values
        y = df.iloc[:, -1].values
        
        # 获取特征名称
        feature_names = df.columns[1:-1].tolist()
        
        # 确保标签是数值类型
        if y.dtype == 'object':
            y = pd.factorize(y)[0]
            
        return X, y, feature_names
    
    except Exception as e:
        print(f"无法从CSV文件加载数据: {e}")
        print("请确保文件路径正确且格式无误")
        # 可选择返回随机数据或终止程序
        raise SystemExit("数据加载失败，请检查CSV文件")
    
X, y, feature_names = load_oil_spill_data()

# iris = load_iris()

# X = iris.data[:, :2]  # Only use sepal length and sepal width
# y = iris.target

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=1)

# Step 2
def classifier():
    clf = SVC(C=0.2, kernel='linear', decision_function_shape='ovr')
    return clf

clf = SVC(C=0.5, kernel='linear')

# Step 3
def train_model(clf, X_train, y_train):
    clf.fit(X_train, y_train.ravel())
    return clf

train_model(clf, X_train, y_train)

# Step 4
def show_accuracy(a, b, tip):
    acc = a.ravel() == b.ravel()
    print("%s Accurarcy:%.3f" % (tip, np.mean(acc)))

def print_accuracy(clf, X_train, y_train, X_test, y_test):
    print("Train Accuracy: %.3f" % clf.score(X_train, y_train))
    print("Test Accuracy: %.3f" % clf.score(X_test, y_test))

    show_accuracy(clf.predict(X_train), y_train, "Train")

    show_accuracy(clf.predict(X_test), y_test, "Test")
    print('decision_function:\n', clf.decision_function(X_train))

# Step 5
def draw(clf, X):
    # 计算主要数据范围 (80% 的数据集中在此区间)
    x1_lower = np.percentile(X[:, 0], 10)  # 10%分位数
    x1_upper = np.percentile(X[:, 0], 90)  # 90%分位数
    x2_lower = np.percentile(X[:, 1], 10)
    x2_upper = np.percentile(X[:, 1], 90)
    
    # 设置绘图范围（聚焦在80%数据区间）
    x1_min, x1_max = x1_lower - 0.1*(x1_upper-x1_lower), x1_upper + 0.1*(x1_upper-x1_lower)
    x2_min, x2_max = x2_lower - 0.1*(x2_upper-x2_lower), x2_upper + 0.1*(x2_upper-x2_lower)
    
    # 生成网格数据
    x1, x2 = np.mgrid[x1_min:x1_max:200j, x2_min:x2_max:200j]
    grid_test = np.stack((x1.flat, x2.flat), axis=1)
    
    # 模型预测
    grid_hat = clf.predict(grid_test)
    grid_hat = grid_hat.reshape(x1.shape)
    
    # 创建颜色映射
    cm_light = mpl.colors.ListedColormap(['#A0FFA0', '#FFA0A0'])
    cm_dark = mpl.colors.ListedColormap(['g', 'r'])
    
    # 绘制决策区域
    plt.pcolormesh(x1, x2, grid_hat, cmap=cm_light, shading='auto')
    
    # 仅绘制主要数据范围内的点（可选）
    in_range_mask = (X[:, 0] >= x1_lower) & (X[:, 0] <= x1_upper) & \
                   (X[:, 1] >= x2_lower) & (X[:, 1] <= x2_upper)
    
    plt.scatter(X[in_range_mask, 0], X[in_range_mask, 1], 
                c=y[in_range_mask], edgecolors='k', s=50, cmap=cm_dark)
    
    plt.xlabel('Feature 1', fontsize=14)
    plt.ylabel('Feature 2', fontsize=14)
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)
    plt.title("SVM Classification (Focus on Main Data Range)", fontsize=16)
    plt.grid(alpha=0.3)
    
    # 添加数据范围说明
    plt.annotate(f'Displaying 80% data range:\nX1: [{x1_lower:.1f}, {x1_upper:.1f}]\nX2: [{x2_lower:.1f}, {x2_upper:.1f}]',
                 xy=(0.98, 0.02), xycoords='axes fraction',
                 ha='right', va='bottom', fontsize=9,
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    
    plt.tight_layout()
    plt.show()

draw(clf, X)