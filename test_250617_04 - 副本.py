import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# Step 1
def load_oil_spill_data(file_path='oil_spill.csv'):
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {df.shape[0]}条记录，{df.shape[1]}个特征")
        
        # 假设最后一列是目标变量(target)，其余列是特征
        X = df.iloc[:, 1:3].values
        y = df.iloc[:, -1].values
        
        # 获取特征名称
        feature_names = df.columns[1:-1].tolist()
        
        # 确保标签是数值类型
        if y.dtype == 'object':
            y = pd.factorize(y)[0]
            
        return X, y, feature_names
    
    except Exception as e:
        print(f"无法从CSV文件加载数据: {e}")
        print("请确保文件路径正确且格式无误")
        # 可选择返回随机数据或终止程序
        raise SystemExit("数据加载失败，请检查CSV文件")
    
X, y, feature_names = load_oil_spill_data()

# iris = load_iris()

# X = iris.data[:, :2]  # Only use sepal length and sepal width
# y = iris.target

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=1)

# Step 2
def classifier(kernel='linear', C=0.5):
    """创建SVM分类器"""
    clf = SVC(C=C, kernel=kernel, decision_function_shape='ovr')
    return clf

# Step 3
def train_model(clf, X_train, y_train):
    """训练模型"""
    clf.fit(X_train, y_train.ravel())
    return clf

# Step 4
def show_accuracy(a, b, tip):
    """显示准确率"""
    acc = a.ravel() == b.ravel()
    print("%s Accuracy: %.3f" % (tip, np.mean(acc)))

def print_accuracy(clf, X_train, y_train, X_test, y_test, kernel_name):
    """打印模型准确率信息"""
    print(f"\n=== {kernel_name.upper()} SVM 结果 ===")
    print("Train Accuracy: %.3f" % clf.score(X_train, y_train))
    print("Test Accuracy: %.3f" % clf.score(X_test, y_test))

    show_accuracy(clf.predict(X_train), y_train, "Train")
    show_accuracy(clf.predict(X_test), y_test, "Test")
    print('Decision Function Shape:', clf.decision_function(X_train).shape)

# Step 5 - 优化版绘图函数
def draw_svm_classification(clf, X, y, kernel_name, save_path=None):
    """
    优化的SVM分类结果可视化函数

    参数:
    - clf: 训练好的SVM分类器
    - X: 特征数据
    - y: 标签数据
    - kernel_name: 核函数名称
    - save_path: 保存路径，如果为None则不保存
    """
    # 计算数据范围，使用更合理的百分位数
    percentile_low, percentile_high = 5, 95
    x1_range = np.percentile(X[:, 0], [percentile_low, percentile_high])
    x2_range = np.percentile(X[:, 1], [percentile_low, percentile_high])

    # 扩展边界以更好地显示决策边界
    margin_factor = 0.15
    x1_margin = (x1_range[1] - x1_range[0]) * margin_factor
    x2_margin = (x2_range[1] - x2_range[0]) * margin_factor

    x1_min, x1_max = x1_range[0] - x1_margin, x1_range[1] + x1_margin
    x2_min, x2_max = x2_range[0] - x2_margin, x2_range[1] + x2_margin

    # 生成网格 - 优化网格密度
    grid_resolution = 300  # 提高分辨率以获得更平滑的决策边界
    x1, x2 = np.mgrid[x1_min:x1_max:complex(0, grid_resolution),
                      x2_min:x2_max:complex(0, grid_resolution)]
    grid_points = np.c_[x1.ravel(), x2.ravel()]

    # 预测网格点
    try:
        grid_predictions = clf.predict(grid_points)
        grid_predictions = grid_predictions.reshape(x1.shape)
    except Exception as e:
        print(f"预测网格点时出错: {e}")
        return

    # 创建图形
    plt.figure(figsize=(12, 8))

    # 设置颜色映射 - 使用更美观的颜色
    unique_labels = np.unique(y)
    n_classes = len(unique_labels)

    if n_classes == 2:
        cm_light = mpl.colors.ListedColormap(['#E8F4FD', '#FFE8E8'])
        cm_dark = mpl.colors.ListedColormap(['#1f77b4', '#ff7f0e'])
    else:
        # 支持多类分类
        colors_light = plt.cm.Set3(np.linspace(0, 1, n_classes))
        colors_dark = plt.cm.Set1(np.linspace(0, 1, n_classes))
        cm_light = mpl.colors.ListedColormap(colors_light)
        cm_dark = mpl.colors.ListedColormap(colors_dark)

    # 绘制决策区域
    plt.contourf(x1, x2, grid_predictions, alpha=0.6, cmap=cm_light, levels=n_classes-1)

    # 绘制决策边界
    plt.contour(x1, x2, grid_predictions, colors='black', linewidths=1, alpha=0.8, levels=n_classes-1)

    # 筛选显示范围内的数据点
    in_range_mask = ((X[:, 0] >= x1_range[0]) & (X[:, 0] <= x1_range[1]) &
                     (X[:, 1] >= x2_range[0]) & (X[:, 1] <= x2_range[1]))

    # 绘制数据点
    scatter = plt.scatter(X[in_range_mask, 0], X[in_range_mask, 1],
                         c=y[in_range_mask], cmap=cm_dark,
                         edgecolors='black', linewidth=0.5, s=60, alpha=0.8)

    # 设置图形属性
    plt.xlabel('Feature 1', fontsize=12, fontweight='bold')
    plt.ylabel('Feature 2', fontsize=12, fontweight='bold')
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)
    plt.title(f'SVM Classification - {kernel_name.upper()} Kernel', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3, linestyle='--')

    # 添加颜色条
    if n_classes > 2:
        plt.colorbar(scatter, label='Class')

    # 添加统计信息
    info_text = (f'Data Range ({percentile_high-percentile_low}%):\n'
                f'Feature 1: [{x1_range[0]:.2f}, {x1_range[1]:.2f}]\n'
                f'Feature 2: [{x2_range[0]:.2f}, {x2_range[1]:.2f}]\n'
                f'Points shown: {np.sum(in_range_mask)}/{len(X)}')

    plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes,
             verticalalignment='top', bbox=dict(boxstyle="round,pad=0.4",
             facecolor="white", edgecolor="gray", alpha=0.9),
             fontsize=9)

    plt.tight_layout()

    # 保存图片而不是显示
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"图片已保存到: {save_path}")

    plt.close()  # 关闭图形以释放内存

# 运行两种SVM方法
kernels = ['linear', 'rbf']
results = {}

for kernel in kernels:
    print(f"\n{'='*50}")
    print(f"运行 {kernel.upper()} SVM")
    print(f"{'='*50}")

    # 创建和训练模型
    clf = classifier(kernel=kernel, C=0.5)
    clf = train_model(clf, X_train, y_train)

    # 打印准确率
    print_accuracy(clf, X_train, y_train, X_test, y_test, kernel)

    # 绘制并保存图片
    save_path = f'svm_{kernel}_classification.png'
    draw_svm_classification(clf, X, y, kernel, save_path)

    # 保存结果
    results[kernel] = {
        'model': clf,
        'train_accuracy': clf.score(X_train, y_train),
        'test_accuracy': clf.score(X_test, y_test)
    }

# 比较结果
print(f"\n{'='*50}")
print("模型比较结果")
print(f"{'='*50}")
for kernel, result in results.items():
    print(f"{kernel.upper()} SVM - 训练准确率: {result['train_accuracy']:.3f}, 测试准确率: {result['test_accuracy']:.3f}")

print(f"\n图片已保存:")
for kernel in kernels:
    print(f"- svm_{kernel}_classification.png")