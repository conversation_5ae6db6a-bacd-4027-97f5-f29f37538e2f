import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# 设置中文字体
plt.rcParams["font.family"] = ["Microsoft YaHei", "SimHei"]
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# Step 1: 加载数据并添加异常值处理
def load_oil_spill_data(file_path='oil_spill.csv', clean_data=True, iqr_threshold=1.5):
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {df.shape[0]}条记录，{df.shape[1]}个特征")
        
        # 假设最后一列是目标变量(target)，其余列是特征
        X = df.iloc[:, 1:-1].values  # 修正：使用所有特征列
        y = df.iloc[:, -1].values
        
        # 获取特征名称
        feature_names = df.columns[1:-1].tolist()
        
        # 确保标签是数值类型
        if y.dtype == 'object':
            y = pd.factorize(y)[0]
        
        # 数据清洗：使用IQR方法检测并过滤异常值
        if clean_data:
            print("正在进行数据清洗...")
            X, y = remove_outliers(X, y, iqr_threshold)
            print(f"清洗后数据: {X.shape[0]}条记录，{X.shape[1]}个特征")
            
        return X, y, feature_names
    
    except Exception as e:
        print(f"无法从CSV文件加载数据: {e}")
        print("请确保文件路径正确且格式无误")
        # 可选择返回随机数据或终止程序
        raise SystemExit("数据加载失败，请检查CSV文件")

# 异常值处理函数
def remove_outliers(X, y, threshold=1.5):
    cleaned_indices = np.ones(X.shape[0], dtype=bool)
    
    for feature_idx in range(X.shape[1]):
        feature_data = X[:, feature_idx]
        
        # 计算四分位数和IQR
        q1 = np.percentile(feature_data, 25)
        q3 = np.percentile(feature_data, 75)
        iqr = q3 - q1
        
        # 计算异常值边界
        lower_bound = q1 - threshold * iqr
        upper_bound = q3 + threshold * iqr
        
        # 标记异常值
        is_outlier = (feature_data < lower_bound) | (feature_data > upper_bound)
        cleaned_indices = cleaned_indices & (~is_outlier)
        
        # 打印异常值信息
        outlier_count = np.sum(is_outlier)
        if outlier_count > 0:
            print(f"特征 {feature_idx} 检测到 {outlier_count} 个异常值")
    
    # 过滤异常值
    return X[cleaned_indices], y[cleaned_indices]

# Step 2: 创建分类器
def classifier():
    clf = SVC(C=0.2, kernel='linear', decision_function_shape='ovr')
    return clf

# Step 3: 训练模型
def train_model(clf, X_train, y_train):
    clf.fit(X_train, y_train.ravel())
    return clf

# Step 4: 评估模型
def show_accuracy(a, b, tip):
    acc = a.ravel() == b.ravel()
    print("%s Accurarcy:%.3f" % (tip, np.mean(acc)))

def print_accuracy(clf, X_train, y_train, X_test, y_test):
    print("训练集准确率: %.3f" % clf.score(X_train, y_train))
    print("测试集准确率: %.3f" % clf.score(X_test, y_test))

    show_accuracy(clf.predict(X_train), y_train, "训练集")
    show_accuracy(clf.predict(X_test), y_test, "测试集")
    print('决策函数值:\n', clf.decision_function(X_train))

# Step 5: 可视化结果
def draw(clf, X, y, feature_names):
    # 选择前两个特征进行可视化
    feature1_idx, feature2_idx = 0, 1
    
    x1_min, x1_max = X[:, feature1_idx].min() - 1, X[:, feature1_idx].max() + 1
    x2_min, x2_max = X[:, feature2_idx].min() - 1, X[:, feature2_idx].max() + 1
    
    # 创建网格
    x1, x2 = np.mgrid[x1_min:x1_max:200j, x2_min:x2_max:200j]
    grid_test = np.stack((x1.flat, x2.flat), axis=1)
    
    # 为了预测，我们只使用前两个特征
    grid_test_full = np.zeros((grid_test.shape[0], X.shape[1]))
    grid_test_full[:, feature1_idx] = grid_test[:, 0]
    grid_test_full[:, feature2_idx] = grid_test[:, 1]
    
    # 填充其他特征的中位数
    for i in range(X.shape[1]):
        if i not in [feature1_idx, feature2_idx]:
            grid_test_full[:, i] = np.median(X[:, i])
    
    # 预测
    z = clf.decision_function(grid_test_full)
    grid_hat = clf.predict(grid_test_full)
    grid_hat = grid_hat.reshape(x1.shape)
    
    # 可视化
    plt.figure(figsize=(12, 8))
    
    # 绘制决策边界
    cm_light = mpl.colors.ListedColormap(['#A0FFA0', '#FFA0A0'])
    cm_dark = mpl.colors.ListedColormap(['g', 'r'])
    plt.pcolormesh(x1, x2, grid_hat, cmap=cm_light, alpha=0.8)
    
    # 绘制散点图
    plt.scatter(X[:, feature1_idx], X[:, feature2_idx], c=y, 
                edgecolors='k', s=50, cmap=cm_dark)
    
    # 设置标签和标题
    plt.xlabel(feature_names[feature1_idx], fontsize=15)
    plt.ylabel(feature_names[feature2_idx], fontsize=15)
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)
    plt.title("SVM分类结果可视化", fontsize=20)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例
    if len(np.unique(y)) <= 3:
        classes = np.unique(y)
        for i, cls in enumerate(classes):
            plt.scatter([], [], c=cm_dark.colors[i], 
                        edgecolors='k', s=50, label=f'类别 {cls}')
        plt.legend(loc='upper right')
    
    plt.tight_layout()
    plt.show()

# 主程序
if __name__ == "__main__":
    # 加载数据并清洗
    X, y, feature_names = load_oil_spill_data(clean_data=True, iqr_threshold=1.5)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=1)
    
    # 创建并训练模型
    clf = SVC(C=0.5, kernel='rbf')
    train_model(clf, X_train, y_train)
    
    # 评估模型
    print_accuracy(clf, X_train, y_train, X_test, y_test)
    
    # 可视化结果
    draw(clf, X, y, feature_names)