import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt

from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC

# Step 1
def load_oil_spill_data(file_path='oil_spill.csv'):
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        print(f"成功加载数据: {df.shape[0]}条记录，{df.shape[1]}个特征")
        
        # 假设最后一列是目标变量(target)，其余列是特征
        X = df.iloc[:, 1:3].values
        y = df.iloc[:, -1].values
        
        # 获取特征名称
        feature_names = df.columns[1:-1].tolist()
        
        # 确保标签是数值类型
        if y.dtype == 'object':
            y = pd.factorize(y)[0]
            
        return X, y, feature_names
    
    except Exception as e:
        print(f"无法从CSV文件加载数据: {e}")
        print("请确保文件路径正确且格式无误")
        # 可选择返回随机数据或终止程序
        raise SystemExit("数据加载失败，请检查CSV文件")
    
X, y, feature_names = load_oil_spill_data()

# iris = load_iris()

# X = iris.data[:, :2]  # Only use sepal length and sepal width
# y = iris.target

X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.3, random_state=1)

# Step 2
def classifier():
    clf = SVC(C=0.2, kernel='linear', decision_function_shape='ovr')
    return clf

clf = SVC(C=0.5, kernel='rbf')

# Step 3
def train_model(clf, X_train, y_train):
    clf.fit(X_train, y_train.ravel())
    return clf

train_model(clf, X_train, y_train)

# Step 4
def show_accuracy(a, b, tip):
    acc = a.ravel() == b.ravel()
    print("%s Accurarcy:%.3f" % (tip, np.mean(acc)))

def print_accuracy(clf, X_train, y_train, X_test, y_test):
    print("Train Accuracy: %.3f" % clf.score(X_train, y_train))
    print("Test Accuracy: %.3f" % clf.score(X_test, y_test))

    show_accuracy(clf.predict(X_train), y_train, "Train")

    show_accuracy(clf.predict(X_test), y_test, "Test")
    print('decision_function:\n', clf.decision_function(X_train))

# Step 5
def draw(clf, X):
    iris_feature  = 'sepal length', 'sepal width', 'petal length', 'petal width'

    x1_min, x1_max = X[:, 0].min() - 1, X[:, 0].max() + 1
    x2_min, x2_max = X[:, 1].min() - 1, X[:, 1].max() + 1
    x1, x2 = np.mgrid[x1_min:x1_max:200j, x2_min:x2_max:200j]
    grid_test = np.stack((x1.flat, x2.flat), axis=1)
    print("grid_test:", grid_test)
    
    z = clf.decision_function(grid_test)
    print("the distance to decision plane:\n", z)
    grid_hat = clf.predict(grid_test)

    print("grid_hat:", grid_hat)
    grid_hat = grid_hat.reshape(x1.shape)

    cm_light = mpl.colors.ListedColormap(['#A0FFA0', '#FFA0A0'])
    cm_dark = mpl.colors.ListedColormap(['g', 'r'])
    plt.pcolormesh(x1, x2, grid_hat, cmap=cm_light)

    plt.scatter(X[:, 0], X[:, 1], c=np.squeeze(y), edgecolors='k', s=50, cmap=cm_dark)

    plt.xlabel(iris_feature[0], fontsize=20)
    plt.ylabel(iris_feature[1], fontsize=20)
    plt.xlim(x1_min, x1_max)
    plt.ylim(x2_min, x2_max)
    plt.title("svm in iris data classification", fontsize=30)
    plt.grid()
    plt.show()

draw(clf, X)